import { ChatHistoryService } from '../chat_history/chat_history'
import { DataService } from '../../getter/getData'
import { ClassGroupSend } from '../flow/schedule/task/classGroupSend'
import { ClassTaskName } from '../flow/schedule/type'
import { UUID } from '../../../../lib/uuid/uuid'
import { Config } from '../../../../config/config'
import { ContextBuilder } from '../agent/context'
import logger from '../../../../model/logger/logger'
import { ChatStateStore, ChatStatStoreManager } from '../../storage/chat_state_store'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import { loadConfigByAccountName } from '../../../../../test/tools/load_config'


describe('Test', function () {
  beforeAll(() => {

  })

  it('context', async () => {
    const chat_id = '7881303548913574_1688856610751737'
    const userSlots = await ContextBuilder.customerPortrait(chat_id)
    const memory = await ContextBuilder.getCustomerMemory(chat_id)

    console.log(userSlots)
    console.log(memory)
  }, 30000)

  it('1231', async () => {
    Config.setting.localTest = false

    new ClassGroupSend().process({
      chatId: UUID.short(),
      userId: UUID.short(),
      name: ClassTaskName.ClassMeetingDay5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:00:00'
      }
    })
  }, 60000)

  it('should pass', async () => {
    const recentConversations = await ChatHistoryService.getRecentConversations('7881301181141895_1688857003605938', 3)
    const chat_history = ChatHistoryService.formatHistoryHelper(recentConversations)

    console.log(chat_history)
  }, 60000)

  it('完课', async () => {
    const chat = await DataService.getChatByWechatName('郭小琴')
    const user = chat[0] as any

    console.log(await DataService.isCompletedCourse(user._id, { day: 2 }))
  }, 60000)

  it('should pass', async () => {
    // 获取 chatId,  qiaoqiao, moer21 , moer22, moer23 85期客户

    // 用 customerPortrait 替换掉  customerMemory, 将 customerPortrait 用所有聊天记录重新提取一下

    const accounts = ['qiaoqiao', 'moer21', 'moer22', 'moer23']
    const accountIds: string[] = []

    for (const account of accounts) {
      const config = await loadConfigByAccountName(account)
      accountIds.push(config.id)
    }

    const courseNo = 85 // 65期客户

    const chatIds: string[] = []

    // 1. 通过客户名称获取chatId
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        wx_id: {
          in: accountIds
        },
        course_no: courseNo
      }
    })

    // 去重
    const uniqueChatIds = [...new Set(chatIds)]
    logger.log(`总共需要修正的客户数量: ${uniqueChatIds.length}`)

    // 3. 对每个chatId进行修正
    for (const chatId of uniqueChatIds) {
      try {
        logger.log(`开始修正客户: ${chatId}`)

        // 初始化状态
        await ChatStatStoreManager.initState(chatId)

        // 获取当前的状态
        const chatState = ChatStateStore.get(chatId)

        // 将customerPortrait的数据移到customerMemory
        if (chatState.customerPortrait && Object.keys(chatState.customerPortrait).length > 0) {
          logger.log(`客户 ${chatId} 将customerPortrait移到customerMemory`)

          // 备份原有的customerMemory（如果有的话）
          const originalMemory = chatState.customerMemory || {}

          // 将customerPortrait的数据作为新的customerMemory
          const newCustomerMemory = chatState.customerPortrait

          // 更新customerMemory
          ChatStateStore.update(chatId, { customerMemory: newCustomerMemory })

          // 清空customerPortrait
          ChatStateStore.update(chatId, { customerPortrait: {} })

          logger.log(`客户 ${chatId} 字段修正完成`)
        } else {
          logger.log(`客户 ${chatId} 的customerPortrait为空，跳过`)
        }

        // 4. 重新拉取所有聊天记录并重新生成customerPortrait
        try {
          logger.log(`客户 ${chatId} 开始重新生成customerPortrait`)

          // 获取所有聊天记录
          const allChatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

          if (allChatHistory && allChatHistory.length > 0) {
            // 重新提取客户画像
            const roundId = UUID.v4()


            logger.log(`客户 ${chatId} customerPortrait重新生成完成`)
          } else {
            logger.log(`客户 ${chatId} 没有聊天记录`)
          }
        } catch (error) {
          logger.log(`客户 ${chatId} 重新生成customerPortrait失败:`, error)
        }

        // 保存状态
        await DataService.saveChat(chatId, chatId.split('_')[0])

        logger.log(`客户 ${chatId} 修正完成`)

      } catch (error) {
        logger.log(`修正客户 ${chatId} 失败:`, error)
      }
    }

    logger.log('所有客户修正完成')
  }, 300000) // 5分钟超时
})